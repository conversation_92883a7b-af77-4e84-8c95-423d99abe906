package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StatisticsMetadataControllerTest {

    @Mock
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Mock
    private TaxesStatisticsViewService taxesStatisticsViewService;

    private StatisticsMetadataController controller;

    @BeforeEach
    void setUp() {
        controller = new StatisticsMetadataController(certificationsStatisticsViewService, taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers without filters")
    void shouldGetCertificationIssuersWithoutFilters() {
        // given
        List<String> expectedIssuers = List.of("Office1", "Office2", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(null, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuers();
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by years only")
    void shouldGetCertificationIssuersFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<String> expectedIssuers = List.of("Office1", "Office2");
        when(certificationsStatisticsViewService.getAvailableIssuersByYears(years)).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(years, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByYears(years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by federal state only")
    void shouldGetCertificationIssuersFilteredByFederalState() {
        // given
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<String> expectedIssuers = List.of("Office1", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuersByFederalState("SH")).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(null, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByFederalState("SH");
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by both years and federal state")
    void shouldGetCertificationIssuersFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<String> expectedIssuers = List.of("Office1", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuersByYearsAndFederalState(years, "SH")).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(years, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByYearsAndFederalState(years, "SH");
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle empty years list")
    void shouldHandleEmptyYearsList() {
        // given
        List<Integer> emptyYears = List.of();
        List<String> expectedIssuers = List.of("Office1", "Office2");
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(emptyYears, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuers();
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle exception when getting certification issuers")
    void shouldHandleExceptionWhenGettingCertificationIssuers() {
        // given
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> controller.statisticsMetadataControllerGetCertificationIssuers(null, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch available certification issuers");
    }

    @Test
    @DisplayName("Should get tax offices without filters")
    void shouldGetTaxOfficesWithoutFilters() {
        // given
        List<String> expectedOffices = List.of("TaxOffice1", "TaxOffice2", "TaxOffice3");
        when(taxesStatisticsViewService.getAvailableOffices()).thenReturn(expectedOffices);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(null, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedOffices);
        verify(taxesStatisticsViewService).getAvailableOffices();
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by years only")
    void shouldGetTaxOfficesFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<String> expectedOffices = List.of("TaxOffice1", "TaxOffice2");
        when(taxesStatisticsViewService.getAvailableOfficesByYears(years)).thenReturn(expectedOffices);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(years, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedOffices);
        verify(taxesStatisticsViewService).getAvailableOfficesByYears(years);
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by federal state only")
    void shouldGetTaxOfficesFilteredByFederalState() {
        // given
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<String> expectedOffices = List.of("TaxOffice1", "TaxOffice3");
        when(taxesStatisticsViewService.getAvailableOfficesByFederalState("SH")).thenReturn(expectedOffices);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(null, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedOffices);
        verify(taxesStatisticsViewService).getAvailableOfficesByFederalState("SH");
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by both years and federal state")
    void shouldGetTaxOfficesFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<String> expectedOffices = List.of("TaxOffice1", "TaxOffice3");
        when(taxesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(years, "SH")).thenReturn(expectedOffices);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(years, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedOffices);
        verify(taxesStatisticsViewService).getAvailableOfficesByYearsAndFederalState(years, "SH");
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle exception when getting tax offices")
    void shouldHandleExceptionWhenGettingTaxOffices() {
        // given
        when(taxesStatisticsViewService.getAvailableOffices()).thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> controller.statisticsMetadataControllerGetTaxesStatistics(null, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch available tax offices");
    }
}
