package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StatisticsMetadataControllerTest {

    @Mock
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Mock
    private TaxesStatisticsViewService taxesStatisticsViewService;

    private StatisticsMetadataController controller;

    @BeforeEach
    void setUp() {
        controller = new StatisticsMetadataController(certificationsStatisticsViewService, taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers without filters")
    void shouldGetCertificationIssuersWithoutFilters() {
        // given
        List<String> expectedIssuers = List.of("Office1", "Office2", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(null, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuers();
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by years only")
    void shouldGetCertificationIssuersFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<CertificationsStatisticsView> views = List.of(
                createCertificationView("SH", "Office1", 2023, 5),
                createCertificationView("NI", "Office2", 2024, 3),
                createCertificationView("SH", "Office1", 2024, 2)
        );
        when(certificationsStatisticsViewService.getStatisticsByYears(years)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(years, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("Office1", "Office2");
        verify(certificationsStatisticsViewService).getStatisticsByYears(years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by federal state only")
    void shouldGetCertificationIssuersFilteredByFederalState() {
        // given
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<Integer> allYears = List.of(2022, 2023, 2024);
        List<CertificationsStatisticsView> views = List.of(
                createCertificationView("SH", "Office1", 2023, 5),
                createCertificationView("SH", "Office3", 2024, 2)
        );
        when(certificationsStatisticsViewService.getAvailableYears()).thenReturn(allYears);
        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears("SH", allYears)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(null, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("Office1", "Office3");
        verify(certificationsStatisticsViewService).getAvailableYears();
        verify(certificationsStatisticsViewService).getStatisticsByFederalStateAndYears("SH", allYears);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get certification issuers filtered by both years and federal state")
    void shouldGetCertificationIssuersFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<CertificationsStatisticsView> views = List.of(
                createCertificationView("SH", "Office1", 2023, 5),
                createCertificationView("SH", "Office3", 2024, 2)
        );
        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears("SH", years)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(years, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("Office1", "Office3");
        verify(certificationsStatisticsViewService).getStatisticsByFederalStateAndYears("SH", years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle empty years list as no filter")
    void shouldHandleEmptyYearsListAsNoFilter() {
        // given
        List<Integer> emptyYears = List.of();
        List<String> expectedIssuers = List.of("Office1", "Office2");
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenReturn(expectedIssuers);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetCertificationIssuers(emptyYears, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuers();
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle exception when getting certification issuers")
    void shouldHandleExceptionWhenGettingCertificationIssuers() {
        // given
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> controller.statisticsMetadataControllerGetCertificationIssuers(null, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch available certification issuers");
    }

    @Test
    @DisplayName("Should get tax offices without filters")
    void shouldGetTaxOfficesWithoutFilters() {
        // given
        List<String> expectedOffices = List.of("TaxOffice1", "TaxOffice2", "TaxOffice3");
        when(taxesStatisticsViewService.getAvailableOffices()).thenReturn(expectedOffices);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(null, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedOffices);
        verify(taxesStatisticsViewService).getAvailableOffices();
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by years only")
    void shouldGetTaxOfficesFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<TaxesStatisticsView> views = List.of(
                createTaxView("SH", "TaxOffice1", 2023),
                createTaxView("NI", "TaxOffice2", 2024),
                createTaxView("SH", null, 2024) // null office should be filtered out
        );
        when(taxesStatisticsViewService.getStatisticsByYears(years)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(years, null);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("TaxOffice1", "TaxOffice2");
        verify(taxesStatisticsViewService).getStatisticsByYears(years);
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by federal state only")
    void shouldGetTaxOfficesFilteredByFederalState() {
        // given
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<Integer> allYears = List.of(2022, 2023, 2024);
        List<TaxesStatisticsView> views = List.of(
                createTaxView("SH", "TaxOffice1", 2023),
                createTaxView("SH", "TaxOffice3", 2024)
        );
        when(taxesStatisticsViewService.getAvailableYears()).thenReturn(allYears);
        when(taxesStatisticsViewService.getStatisticsByFederalStateAndYears("SH", allYears)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(null, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("TaxOffice1", "TaxOffice3");
        verify(taxesStatisticsViewService).getAvailableYears();
        verify(taxesStatisticsViewService).getStatisticsByFederalStateAndYears("SH", allYears);
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should get tax offices filtered by both years and federal state")
    void shouldGetTaxOfficesFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        FederalStateAbbreviation federalState = FederalStateAbbreviation.SH;
        List<TaxesStatisticsView> views = List.of(
                createTaxView("SH", "TaxOffice1", 2023),
                createTaxView("SH", "TaxOffice3", 2024)
        );
        when(taxesStatisticsViewService.getStatisticsByFederalStateAndYears("SH", years)).thenReturn(views);

        // when
        ResponseEntity<List<String>> response = controller.statisticsMetadataControllerGetTaxesStatistics(years, federalState);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsExactly("TaxOffice1", "TaxOffice3");
        verify(taxesStatisticsViewService).getStatisticsByFederalStateAndYears("SH", years);
        verifyNoMoreInteractions(taxesStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle exception when getting tax offices")
    void shouldHandleExceptionWhenGettingTaxOffices() {
        // given
        when(taxesStatisticsViewService.getAvailableOffices()).thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> controller.statisticsMetadataControllerGetTaxesStatistics(null, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch available tax offices");
    }

    private CertificationsStatisticsView createCertificationView(String federalState, String issuer, int year, int count) {
        CertificationsStatisticsView view = new CertificationsStatisticsView();
        view.setFederalState(federalState);
        view.setIssuer(issuer);
        view.setYear(year);
        view.setCount(count);
        return view;
    }

    private TaxesStatisticsView createTaxView(String federalState, String office, int year) {
        TaxesStatisticsView view = new TaxesStatisticsView();
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setYear(year);
        return view;
    }
}
