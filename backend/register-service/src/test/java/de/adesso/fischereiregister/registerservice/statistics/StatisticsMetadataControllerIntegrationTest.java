package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@ActiveProfiles("test")
class StatisticsMetadataControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Autowired
    private TaxesStatisticsViewService taxesStatisticsViewService;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // Clean up data before each test
        certificationsStatisticsViewService.deleteAll();
        taxesStatisticsViewService.deleteAll();
    }

    @Test
    @DisplayName("Should return certification issuers without filters")
    void shouldReturnCertificationIssuersWithoutFilters() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office3", 2024);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$", containsInAnyOrder("Office1", "Office2", "Office3")));
    }

    @Test
    @DisplayName("Should return certification issuers filtered by year")
    void shouldReturnCertificationIssuersFilteredByYear() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office3", 2024);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers")
                        .param("year", "2023"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("Office1", "Office2")));
    }

    @Test
    @DisplayName("Should return certification issuers filtered by federal state")
    void shouldReturnCertificationIssuersFilteredByFederalState() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office3", 2024);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers")
                        .param("federalState", "SH"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("Office1", "Office3")));
    }

    @Test
    @DisplayName("Should return certification issuers filtered by year and federal state")
    void shouldReturnCertificationIssuersFilteredByYearAndFederalState() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office3", 2024);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers")
                        .param("year", "2023")
                        .param("federalState", "SH"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0]", is("Office1")));
    }

    @Test
    @DisplayName("Should return empty list when no certification issuers match filters")
    void shouldReturnEmptyListWhenNoCertificationIssuersMatchFilters() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers")
                        .param("year", "2025"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    @DisplayName("Should return tax offices without filters")
    void shouldReturnTaxOfficesWithoutFilters() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("NI", "TaxOffice2", SubmissionType.ANALOG, 1, 2023, 150.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice3", SubmissionType.ONLINE, 1, 2024, 200.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$", containsInAnyOrder("TaxOffice1", "TaxOffice2", "TaxOffice3")));
    }

    @Test
    @DisplayName("Should return tax offices filtered by year")
    void shouldReturnTaxOfficesFilteredByYear() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("NI", "TaxOffice2", SubmissionType.ANALOG, 1, 2023, 150.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice3", SubmissionType.ONLINE, 1, 2024, 200.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices")
                        .param("year", "2023"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("TaxOffice1", "TaxOffice2")));
    }

    @Test
    @DisplayName("Should return tax offices filtered by federal state")
    void shouldReturnTaxOfficesFilteredByFederalState() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("NI", "TaxOffice2", SubmissionType.ANALOG, 1, 2023, 150.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice3", SubmissionType.ONLINE, 1, 2024, 200.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices")
                        .param("federalState", "SH"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("TaxOffice1", "TaxOffice3")));
    }

    @Test
    @DisplayName("Should return tax offices filtered by year and federal state")
    void shouldReturnTaxOfficesFilteredByYearAndFederalState() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("NI", "TaxOffice2", SubmissionType.ANALOG, 1, 2023, 150.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice3", SubmissionType.ONLINE, 1, 2024, 200.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices")
                        .param("year", "2023")
                        .param("federalState", "SH"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0]", is("TaxOffice1")));
    }

    @Test
    @DisplayName("Should return empty list when no tax offices match filters")
    void shouldReturnEmptyListWhenNoTaxOfficesMatchFilters() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices")
                        .param("year", "2025"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    @DisplayName("Should handle multiple years parameter")
    void shouldHandleMultipleYearsParameter() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2024);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office3", 2025);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers")
                        .param("year", "2023", "2024"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("Office1", "Office2")));
    }

    @Test
    @DisplayName("Should filter out null offices from tax statistics")
    void shouldFilterOutNullOfficesFromTaxStatistics() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", null, SubmissionType.ANALOG, 1, 2023, 150.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0]", is("TaxOffice1")));
    }

    @Test
    @DisplayName("Should return distinct issuers when duplicates exist")
    void shouldReturnDistinctIssuersWhenDuplicatesExist() throws Exception {
        // given
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2023);
        certificationsStatisticsViewService.updateOrCreateStatistic("SH", "Office1", 2024); // Same issuer, different year
        certificationsStatisticsViewService.updateOrCreateStatistic("NI", "Office2", 2023);

        // when & then
        mockMvc.perform(get("/statistics/metadata/certification-issuers"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("Office1", "Office2")));
    }

    @Test
    @DisplayName("Should return distinct offices when duplicates exist")
    void shouldReturnDistinctOfficesWhenDuplicatesExist() throws Exception {
        // given
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ONLINE, 1, 2023, 100.0);
        taxesStatisticsViewService.updateOrCreateStatistic("SH", "TaxOffice1", SubmissionType.ANALOG, 2, 2023, 150.0); // Same office, different submission type
        taxesStatisticsViewService.updateOrCreateStatistic("NI", "TaxOffice2", SubmissionType.ONLINE, 1, 2023, 200.0);

        // when & then
        mockMvc.perform(get("/statistics/metadata/offices"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$", containsInAnyOrder("TaxOffice1", "TaxOffice2")));
    }
}
