package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.licenses_statistics.eventhandling.LicensesStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class LicensesStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private LicensesStatisticsViewEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create events and call handler directly
        Instant currentYearInstant = LocalDateTime.of(CURRENT_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);
        Instant previousYearInstant = LocalDateTime.of(PREVIOUS_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

        // Create regular license events
        RegularLicenseCreatedEvent regularLicenseCreatedEventSH1 = createRegularLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH);
        RegularLicenseCreatedEvent regularLicenseCreatedEventSH2 = createRegularLicenseEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH);
        RegularLicenseCreatedEvent regularLicenseCreatedEventHH = createRegularLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_HH);
        RegularLicenseCreatedEvent regularLicensePreviousYearSH = createRegularLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH);

        // Create vacation license events
        VacationLicenseCreatedEvent vacationLicenseCreatedEventSH = createVacationLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH);
        VacationLicenseCreatedEvent vacationLicenseCreatedEventHH = createVacationLicenseEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_HH);

        // Create limited license events
        LimitedLicenseCreatedEvent limitedLicenseCreatedEventSH = createLimitedLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH);

        // Create digitized license event
        RegularLicenseDigitizedEvent regularLicenseDigitizedEventSH = createRegularLicenseDigitizedEvent(TEST_FEDERAL_STATE_SH);

        // Call event handler directly
        eventHandler.on(regularLicenseCreatedEventSH1, currentYearInstant);
        eventHandler.on(regularLicenseCreatedEventSH2, currentYearInstant);
        eventHandler.on(regularLicenseCreatedEventHH, currentYearInstant);
        eventHandler.on(vacationLicenseCreatedEventSH, currentYearInstant);
        eventHandler.on(vacationLicenseCreatedEventHH, currentYearInstant);
        eventHandler.on(limitedLicenseCreatedEventSH, currentYearInstant);
        eventHandler.on(regularLicensePreviousYearSH, previousYearInstant);
        eventHandler.on(regularLicenseDigitizedEventSH, currentYearInstant);
    }

    private RegularLicenseCreatedEvent createRegularLicenseEvent(SubmissionType submissionType, String federalState) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();

        // Create a person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create RegularLicenseCreatedEvent
        return new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                "salt",
                null, // consentInfo
                person, // person
                List.of(), // fees
                List.of(), // taxes
                license,
                List.of(), // identificationDocuments
                jurisdiction,
                "Test Office", // issuedByOffice
                "Test Address", // issuedByAddress
                null, // inboxReference
                submissionType
        );
    }

    private VacationLicenseCreatedEvent createVacationLicenseEvent(SubmissionType submissionType, String federalState) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.VACATION);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create VacationLicenseCreatedEvent
        return new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null, // person
                "salt",
                null, // consentInfo
                List.of(), // fees
                List.of(), // taxes
                List.of(), // identificationDocuments
                license,
                "Test Office", // issuedByOffice
                null, // inboxReference
                submissionType
        );
    }

    private LimitedLicenseCreatedEvent createLimitedLicenseEvent(SubmissionType submissionType, String federalState) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.LIMITED);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create LimitedLicenseCreatedEvent
        return new LimitedLicenseCreatedEvent(
                UUID.randomUUID(),
                "salt",
                null, // limitedLicenseConsentInfo
                null, // person
                List.of(), // fees
                List.of(), // taxes
                license,
                List.of(), // identificationDocuments
                jurisdiction,
                "Test Office", // issuedByOffice
                "Test Address", // issuedByAddress
                null, // inboxReference
                submissionType
        );
    }

    private RegularLicenseDigitizedEvent createRegularLicenseDigitizedEvent(String federalState) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.REGULAR);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create a person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create RegularLicenseDigitizedEvent
        return new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                "salt",
                person,
                jurisdiction,
                license,
                List.of(), // fees
                List.of(), // taxes
                List.of(), // qualificationsProofs
                List.of(), // identificationDocuments
                null, // consentInfo
                "Test Office", // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular
            Verify that the regular licenses statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetRegularLicensesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data", hasSize(greaterThanOrEqualTo(2)))) // At least 2 submission types
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Regular licenses with ANALOG submission (1 SH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Regular licenses with ONLINE submission (1 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular with federal state parameter
            Verify that the regular licenses statistics endpoint correctly filters by federal state.
            """)
    void callGetRegularLicensesStatisticsWithFederalStateFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Regular licenses with ANALOG submission (1 HH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular with year parameter
            Verify that the regular licenses statistics endpoint correctly filters by year.
            """)
    void callGetRegularLicensesStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Regular licenses with ANALOG submission (1 SH in previous year)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular with multiple year parameters
            Verify that the regular licenses statistics endpoint correctly handles multiple year parameters.
            """)
    void callGetRegularLicensesStatisticsWithMultipleYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(2))) // Years of data (2)
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)); // Second result year (previous year)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular without federalState parameter
            Verify that the regular licenses statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetRegularLicensesStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(2)))); // Regular licenses with ANALOG submission (1 SH + 1 HH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular without parameters
            Verify that the regular licenses statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetRegularLicensesStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR )) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[*].data").exists()); // Data objects exist
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular with non-existent year parameter
            Verify that the regular licenses statistics endpoint returns zero-filled data for a non-existent year.
            """)
    void callGetRegularLicensesStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isNotEmpty()) // Non-empty array with zero-filled data
                .andExpect(jsonPath("$[0].year").value(Integer.parseInt(nonExistentYear))) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(0))) // Zero count for ONLINE submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(0))); // Zero count for ANALOG submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/vacation
            Verify that the vacation licenses statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetVacationLicensesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/vacation")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Vacation licenses with ANALOG submission (1 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/limited
            Verify that the limited licenses statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetLimitedLicensesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/limited")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Limited licenses with ANALOG submission (1 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular with digitized licenses
            Verify that the regular licenses statistics endpoint correctly counts digitized licenses.
            """)
    void callGetRegularLicensesStatisticsWithDigitizedLicenses() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Regular licenses with ANALOG submission (1 created + 1 digitized)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Regular licenses with ONLINE submission (1 created)
    }
}
