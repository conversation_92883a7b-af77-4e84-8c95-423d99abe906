package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsMetadataController implements api.StatisticsApi {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;
    private final TaxesStatisticsViewService taxesStatisticsViewService;

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetCertificationIssuers(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available certification issuers for years: {} and federalState: {}", year, federalState);

            List<String> availableIssuers;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableIssuers = certificationsStatisticsViewService.getAvailableIssuersByYearsAndFederalState(year, federalState.getValue());
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableIssuers = certificationsStatisticsViewService.getAvailableIssuersByYears(year);
            } else if (federalState != null) {
                // Filter by federal state only
                availableIssuers = certificationsStatisticsViewService.getAvailableIssuersByFederalState(federalState.getValue());
            } else {
                // No filters
                availableIssuers = certificationsStatisticsViewService.getAvailableIssuers();
            }

            log.info("Found {} available certification issuers", availableIssuers.size());
            return ResponseEntity.ok(availableIssuers);

        } catch (Exception e) {
            log.error("Error fetching available certification issuers: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available certification issuers", e);
        }
    }

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetTaxesStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available tax offices for years: {} and federalState: {}", year, federalState);

            List<String> availableOffices;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableOffices = taxesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(year, federalState.getValue());
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableOffices = taxesStatisticsViewService.getAvailableOfficesByYears(year);
            } else if (federalState != null) {
                // Filter by federal state only
                availableOffices = taxesStatisticsViewService.getAvailableOfficesByFederalState(federalState.getValue());
            } else {
                // No filters
                availableOffices = taxesStatisticsViewService.getAvailableOffices();
            }

            log.info("Found {} available tax offices", availableOffices.size());
            return ResponseEntity.ok(availableOffices);

        } catch (Exception e) {
            log.error("Error fetching available tax offices: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available tax offices", e);
        }
    }
}
