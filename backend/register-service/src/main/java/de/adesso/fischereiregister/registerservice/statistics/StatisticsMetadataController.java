package de.adesso.fischereiregister.registerservice.statistics;

import api.StatisticsApi;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsMetadataController implements api.StatisticsApi {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;
    private final TaxesStatisticsViewService taxesStatisticsViewService;

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetCertificationIssuers(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available certification issuers for years: {} and federalState: {}", year, federalState);

            List<String> availableIssuers;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableIssuers = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState.getValue(), year)
                        .stream()
                        .map(view -> view.getIssuer())
                        .distinct()
                        .sorted()
                        .toList();
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableIssuers = certificationsStatisticsViewService.getStatisticsByYears(year)
                        .stream()
                        .map(view -> view.getIssuer())
                        .distinct()
                        .sorted()
                        .toList();
            } else if (federalState != null) {
                // Filter by federal state only - get all years first, then filter
                List<Integer> allYears = certificationsStatisticsViewService.getAvailableYears();
                availableIssuers = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState.getValue(), allYears)
                        .stream()
                        .map(view -> view.getIssuer())
                        .distinct()
                        .sorted()
                        .toList();
            } else {
                // No filters - get all available issuers
                availableIssuers = certificationsStatisticsViewService.getAvailableIssuers();
            }

            log.info("Found {} available certification issuers", availableIssuers.size());
            return ResponseEntity.ok(availableIssuers);

        } catch (Exception e) {
            log.error("Error fetching available certification issuers: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available certification issuers", e);
        }
    }

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetTaxesStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available tax offices for years: {} and federalState: {}", year, federalState);

            List<String> availableOffices;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableOffices = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState.getValue(), year)
                        .stream()
                        .map(view -> view.getOffice())
                        .filter(office -> office != null)
                        .distinct()
                        .sorted()
                        .toList();
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableOffices = taxesStatisticsViewService.getStatisticsByYears(year)
                        .stream()
                        .map(view -> view.getOffice())
                        .filter(office -> office != null)
                        .distinct()
                        .sorted()
                        .toList();
            } else if (federalState != null) {
                // Filter by federal state only - get all years first, then filter
                List<Integer> allYears = taxesStatisticsViewService.getAvailableYears();
                availableOffices = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState.getValue(), allYears)
                        .stream()
                        .map(view -> view.getOffice())
                        .filter(office -> office != null)
                        .distinct()
                        .sorted()
                        .toList();
            } else {
                // No filters - get all available offices
                availableOffices = taxesStatisticsViewService.getAvailableOffices();
            }

            log.info("Found {} available tax offices", availableOffices.size());
            return ResponseEntity.ok(availableOffices);

        } catch (Exception e) {
            log.error("Error fetching available tax offices: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available tax offices", e);
        }
    }
}
