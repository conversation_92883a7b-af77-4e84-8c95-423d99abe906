package de.adesso.fischereiregister.view.certifications_statistics.services;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;

import java.util.List;

/**
 * Manages certification statistics, allowing updates or creation of entries.
 */
public interface CertificationsStatisticsViewService {
    /**
     * Updates or creates a certification statistic entry. If an entry exists for the given parameters, its count is incremented.
     * Otherwise, a new entry is created with a count of 1.
     *
     * @param federalState The federal state for the statistic.
     * @param issuer       The issuer (office) for the statistic.
     * @param year         The year that the statistics account for
     */
    void updateOrCreateStatistic(String federalState, String issuer, int year);

    /**
     * Retrieves certification statistics for the specified federal state and years.
     *
     * @param federalState The federal state to filter by.
     * @param years        The years to include in the results.
     * @return A list of certification statistics views matching the criteria.
     */
    List<CertificationsStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years);

    /**
     * Retrieves certification statistics for the specified issuer and years.
     *
     * @param issuer The issuer (office) to filter by.
     * @param years  The years to include in the results.
     * @return A list of certification statistics views matching the criteria.
     */
    List<CertificationsStatisticsView> getStatisticsByIssuerAndYears(String issuer, List<Integer> years);

    /**
     * Retrieves certification statistics for the specified years.
     *
     * @param years The years to include in the results.
     * @return A list of certification statistics views matching the criteria.
     */
    List<CertificationsStatisticsView> getStatisticsByYears(List<Integer> years);

    /**
     * Retrieves all available years for which certification statistics exist.
     *
     * @return A list of years.
     */
    List<Integer> getAvailableYears();

    /**
     * Retrieves all available certification issuers (offices) with optional filtering.
     *
     * @param years        Optional list of years to filter by. If null or empty, all years are considered.
     * @param federalState Optional federal state to filter by. If null, all federal states are considered.
     * @return A list of distinct issuer names sorted alphabetically.
     */
    List<String> getAvailableIssuers(List<Integer> years, String federalState);

    /**
     * Deletes all Certification Statistics entries in the View (Projection).
     */
    void deleteAll();
}
