package de.adesso.fischereiregister.view.certifications_statistics.persistence;

import java.util.*;
import java.util.stream.Collectors;

public class InMemoryCertificationsStatisticsViewRepository implements CertificationsStatisticsViewRepository {

    private final Map<Long, CertificationsStatisticsView> database = new HashMap<>();
    private long idCounter = 1;

    @Override
    public Optional<CertificationsStatisticsView> findByFederalStateAndIssuerAndYear(
            String federalState, String issuer, int year) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> view.getIssuer().equals(issuer))
                .filter(view -> view.getYear() == year)
                .findFirst();
    }

    @Override
    public List<CertificationsStatisticsView> findByFederalStateAndYearIn(String federalState, List<Integer> years) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<CertificationsStatisticsView> findByIssuerAndYearIn(String issuer, List<Integer> years) {
        return database.values().stream()
                .filter(view -> view.getIssuer().equals(issuer))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<CertificationsStatisticsView> findByYearIn(List<Integer> years) {
        return database.values().stream()
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return database.values().stream()
                .map(CertificationsStatisticsView::getYear)
                .distinct()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctIssuers() {
        return database.values().stream()
                .map(CertificationsStatisticsView::getIssuer)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public <S extends CertificationsStatisticsView> S save(S entity) {
        if (entity.getId() == null) {
            entity.setId(idCounter++);
        }
        database.put(entity.getId(), entity);
        return entity;
    }

    @Override
    public <S extends CertificationsStatisticsView> Iterable<S> saveAll(Iterable<S> entities) {
        entities.forEach(this::save);
        return entities;
    }

    @Override
    public Optional<CertificationsStatisticsView> findById(Long id) {
        return Optional.ofNullable(database.get(id));
    }

    @Override
    public boolean existsById(Long id) {
        return database.containsKey(id);
    }

    @Override
    public Iterable<CertificationsStatisticsView> findAll() {
        return database.values();
    }

    @Override
    public Iterable<CertificationsStatisticsView> findAllById(Iterable<Long> ids) {
        List<CertificationsStatisticsView> result = new ArrayList<>();
        ids.forEach(id -> {
            if (database.containsKey(id)) {
                result.add(database.get(id));
            }
        });
        return result;
    }

    @Override
    public long count() {
        return database.size();
    }

    @Override
    public void deleteById(Long id) {
        database.remove(id);
    }

    @Override
    public void delete(CertificationsStatisticsView entity) {
        if (entity.getId() != null) {
            database.remove(entity.getId());
        }
    }

    @Override
    public void deleteAllById(Iterable<? extends Long> ids) {
        ids.forEach(database::remove);
    }

    @Override
    public void deleteAll(Iterable<? extends CertificationsStatisticsView> entities) {
        entities.forEach(this::delete);
    }

    @Override
    public void deleteAll() {
        database.clear();
    }
}
