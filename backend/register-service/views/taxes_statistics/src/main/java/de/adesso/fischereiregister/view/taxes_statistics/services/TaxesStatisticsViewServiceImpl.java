package de.adesso.fischereiregister.view.taxes_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class TaxesStatisticsViewServiceImpl implements TaxesStatisticsViewService {

    private final TaxesStatisticsViewRepository repository;

    @Override
    public void updateOrCreateStatistic(String federalState, String office, SubmissionType source, int duration, int year, double revenue) {
        // Try to find an existing entry
        TaxesStatisticsView view = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(federalState, office, source, duration, year)
                .orElse(null);

        if (view != null) {
            view.setCount(view.getCount() + 1);
            view.setRevenue(view.getRevenue() + revenue);
        } else {
            view = new TaxesStatisticsView();
            view.setFederalState(federalState);
            view.setOffice(office);
            view.setSource(source);
            view.setDuration(duration);
            view.setYear(year);
            view.setCount(1);
            view.setRevenue(revenue);
        }

        repository.save(view);
    }

    @Override
    public List<TaxesStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years) {
        return repository.findByFederalStateAndYearIn(federalState, years);
    }

    @Override
    public List<TaxesStatisticsView> getStatisticsByOfficeAndYears(String office, List<Integer> years) {
        return repository.findByOfficeAndYearIn(office, years);
    }

    @Override
    public List<TaxesStatisticsView> getStatisticsByYears(List<Integer> years) {
        return repository.findByYearIn(years);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctYears();
    }

    @Override
    public List<String> getAvailableOffices(List<Integer> years, String federalState) {
        List<TaxesStatisticsView> statisticsViews;

        // Determine which years to query
        List<Integer> yearsToQuery = (years != null && !years.isEmpty()) ? years : getAvailableYears();

        // Apply filtering based on parameters
        if (federalState != null) {
            statisticsViews = getStatisticsByFederalStateAndYears(federalState, yearsToQuery);
        } else {
            statisticsViews = getStatisticsByYears(yearsToQuery);
        }

        // Extract distinct offices, filter out nulls, and sort them
        return statisticsViews.stream()
                .map(TaxesStatisticsView::getOffice)
                .filter(office -> office != null)
                .distinct()
                .sorted()
                .toList();
    }

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }
}
