package de.adesso.fischereiregister.view.taxes_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;

import java.util.List;

public interface TaxesStatisticsViewService {
    /**
     * Updates or creates a tax statistics entry. If an entry exists for the given parameters,
     * its count is incremented, and the revenue is increased by the specified amount.
     * Otherwise, a new entry is created with a count of 1.
     * <p>
     * Each tax is paid for a specific range of years, and the statistics are aggregated
     * based on the duration of these ranges.
     *
     * @param federalState The federal state for the statistic.
     * @param office       The office responsible for the statistic (nullable).
     * @param source       The submission source (e.g., online, in-person).
     * @param duration     The duration of the paid tax period.
     * @param year         The year for which the statistics are recorded.
     * @param revenue      The total revenue generated for the tax.
     */
    void updateOrCreateStatistic(String federalState, String office, SubmissionType source, int duration, int year, double revenue);

    /**
     * Retrieves tax statistics for the specified federal state and years.
     *
     * @param federalState The federal state to filter by.
     * @param years        The years to include in the results.
     * @return A list of tax statistics views matching the criteria.
     */
    List<TaxesStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years);

    /**
     * Retrieves tax statistics for the specified office and years.
     *
     * @param office The office to filter by.
     * @param years  The years to include in the results.
     * @return A list of tax statistics views matching the criteria.
     */
    List<TaxesStatisticsView> getStatisticsByOfficeAndYears(String office, List<Integer> years);

    /**
     * Retrieves tax statistics for the specified years.
     *
     * @param years The years to include in the results.
     * @return A list of tax statistics views matching the criteria.
     */
    List<TaxesStatisticsView> getStatisticsByYears(List<Integer> years);

    /**
     * Retrieves all available years for which tax statistics exist.
     *
     * @return A list of years.
     */
    List<Integer> getAvailableYears();

    /**
     * Retrieves all available tax offices without any filtering.
     *
     * @return A list of distinct office names sorted alphabetically, excluding null values.
     */
    List<String> getAvailableOffices();

    /**
     * Retrieves available tax offices filtered by years.
     *
     * @param years The years to filter by.
     * @return A list of distinct office names sorted alphabetically, excluding null values.
     */
    List<String> getAvailableOfficesByYears(List<Integer> years);

    /**
     * Retrieves available tax offices filtered by federal state.
     *
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically, excluding null values.
     */
    List<String> getAvailableOfficesByFederalState(String federalState);

    /**
     * Retrieves available tax offices filtered by both years and federal state.
     *
     * @param years        The years to filter by.
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically, excluding null values.
     */
    List<String> getAvailableOfficesByYearsAndFederalState(List<Integer> years, String federalState);

    /**
     * Deletes all Fees Statistics entries in the View (Projection).
     */
    void deleteAll();
}
